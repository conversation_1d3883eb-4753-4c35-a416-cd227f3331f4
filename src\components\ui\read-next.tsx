import { useState, useEffect } from 'react';
import { type BlogArticle } from '@/types/blog';
import { getRelatedArticles } from '@/lib/blog-api';
import { BlogCard } from '@/components/ui/blog-card';

interface ReadNextProps {
  currentArticle: BlogArticle;
}

export function ReadNext({ currentArticle }: ReadNextProps) {
  const [relatedArticles, setRelatedArticles] = useState<BlogArticle[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadRelatedArticles = async () => {
      try {
        const articles = await getRelatedArticles(currentArticle, 3);
        setRelatedArticles(articles);
      } catch (error) {
        console.error('Error loading related articles:', error);
      } finally {
        setLoading(false);
      }
    };

    loadRelatedArticles();
  }, [currentArticle]);

  if (loading) {
    return (
      <div className="mt-16 pt-12 border-t border-border">
        <h2 className="text-2xl font-bold text-foreground mb-8">Read this next</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="bg-muted rounded-md aspect-video animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded w-2/3 animate-pulse" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (relatedArticles.length === 0) {
    return null;
  }

  return (
    <div className="mt-16 pt-12 border-t border-border">
      <h2 className="text-2xl font-bold text-foreground mb-8">Read this next</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {relatedArticles.map((article) => (
          <BlogCard key={article.id} article={article} />
        ))}
      </div>
    </div>
  );
}
