import { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  ogImage?: string;
  ogType?: string;
  canonicalUrl?: string;
  jsonLd?: object;
}

export const SEOHead = ({
  title = "Search & AI-Marketing | FunnelVision Agency",
  description = "Search & AI-marketing that turns customer intent into margin—at scale. Expert paid search, AI discovery, CRO, and web development services.",
  ogImage = "/assets/og-default.jpg",
  ogType = "website",
  canonicalUrl,
  jsonLd
}: SEOHeadProps) => {
  useEffect(() => {
    // Update document title
    document.title = title;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', description);
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute('content', title);
    }

    const ogDesc = document.querySelector('meta[property="og:description"]');
    if (ogDesc) {
      ogDesc.setAttribute('content', description);
    }

    const ogImageTag = document.querySelector('meta[property="og:image"]');
    if (ogImageTag) {
      ogImageTag.setAttribute('content', ogImage);
    }

    const ogTypeTag = document.querySelector('meta[property="og:type"]');
    if (ogTypeTag) {
      ogTypeTag.setAttribute('content', ogType);
    }

    // Update Twitter Card tags
    const twitterImage = document.querySelector('meta[name="twitter:image"]');
    if (twitterImage) {
      twitterImage.setAttribute('content', ogImage);
    }

    // Add canonical URL if provided
    if (canonicalUrl) {
      let canonical = document.querySelector('link[rel="canonical"]');
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.setAttribute('rel', 'canonical');
        document.head.appendChild(canonical);
      }
      canonical.setAttribute('href', canonicalUrl);
    }

    // Add JSON-LD structured data if provided
    if (jsonLd) {
      let script = document.querySelector('script[type="application/ld+json"]');
      if (!script) {
        script = document.createElement('script');
        script.setAttribute('type', 'application/ld+json');
        document.head.appendChild(script);
      }
      script.textContent = JSON.stringify(jsonLd);
    }
  }, [title, description, ogImage, ogType, canonicalUrl, jsonLd]);

  return null; // This component doesn't render anything
};

// Organization JSON-LD schema for FunnelVision
export const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "FunnelVision",
  "alternateName": "FunnelVision Agency",
  "url": "https://funnelvision.com",
  "logo": "https://funnelvision.com/images/logos/funnelvision-logo.svg",
  "description": "Search & AI-marketing agency that turns customer intent into margin—at scale.",
  "foundingDate": "2020",
  "sameAs": [
    "https://twitter.com/funnelvisionads",
    "https://linkedin.com/company/funnelvision"
  ],
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "url": "https://funnelvision.com/book-a-call"
  },
  "areaServed": "Worldwide",
  "serviceType": [
    "AI Discovery & Search",
    "Paid Search Orchestration", 
    "CRO & UX Loop",
    "Web Development",
    "SEO & Content Engineering",
    "Data & Analytics"
  ]
};

// Service page schema generator
export const createServiceSchema = (serviceName: string, description: string) => ({
  "@context": "https://schema.org",
  "@type": "Service",
  "name": serviceName,
  "description": description,
  "provider": {
    "@type": "Organization",
    "name": "FunnelVision",
    "url": "https://funnelvision.com"
  },
  "areaServed": "Worldwide",
  "serviceType": "Digital Marketing"
});

// Case study schema generator
export const createCaseStudySchema = (title: string, client: string, results: string[]) => ({
  "@context": "https://schema.org",
  "@type": "CaseStudy",
  "name": title,
  "about": {
    "@type": "Organization",
    "name": client
  },
  "author": {
    "@type": "Organization",
    "name": "FunnelVision",
    "url": "https://funnelvision.com"
  },
  "result": results,
  "datePublished": new Date().toISOString().split('T')[0]
});
