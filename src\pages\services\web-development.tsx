import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { Button } from "@/components/ui/button";
import { MoveRight, Code, Smartphone, Zap, Shield } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainGrid = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">The Challenge</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="p-6 bg-white rounded-card border">
          <Zap className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Slow Performance</h3>
          <p className="text-muted-foreground">Your website loads slowly, causing visitors to leave before they see your value proposition or make a purchase.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <Smartphone className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Poor Mobile Experience</h3>
          <p className="text-muted-foreground">Your site doesn't work well on mobile devices, losing potential customers who browse and buy on their phones.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <Shield className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Technical Debt</h3>
          <p className="text-muted-foreground">Outdated code, security vulnerabilities, and maintenance issues hold back your growth and create risks.</p>
        </div>
      </div>
    </div>
  </section>
);

const MethodSteps = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Our Method</h2>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">1</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Performance-First Architecture</h3>
          <p className="text-muted-foreground">We build with modern frameworks and optimization techniques that ensure lightning-fast load times.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">2</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Conversion-Focused Design</h3>
          <p className="text-muted-foreground">Every element is designed and positioned to guide users toward your desired actions and business goals.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">3</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Scalable Infrastructure</h3>
          <p className="text-muted-foreground">We build for growth with secure, maintainable code that scales with your business needs.</p>
        </div>
      </div>
    </div>
  </section>
);





const WebDevelopment = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader 
          title="Web Development" 
          subtitle="High-performance websites built for conversion, speed, and seamless user experiences." 
        />
        <PainGrid />
        <MethodSteps />


        <CTADemo />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default WebDevelopment;
