import {
  MagnifyingGlassIcon,
  RocketIcon,
  EyeOpenIcon,
  CodeIcon,
  FileTextIcon,
  BarChartIcon,
} from "@radix-ui/react-icons";

import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";

const features = [
  {
    Icon: MagnifyingGlassIcon,
    name: "AI Discovery & Search",
    description: "Be the first answer—and the last click. Advanced AI-powered search optimization and discovery strategies.",
    href: "/services/ai-discovery",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3",
  },
  {
    Icon: RocketIcon,
    name: "Paid Search Orchestration",
    description: "Strategic paid search campaigns that turn customer intent into profitable conversions at scale.",
    href: "/services/paid-search",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3",
  },
  {
    Icon: EyeOpenIcon,
    name: "CRO & UX Loop",
    description: "Continuous optimization cycles that improve user experience and maximize conversion rates.",
    href: "/services/cro-ux",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4",
  },
  {
    Icon: CodeIcon,
    name: "Web Development",
    description: "High-performance websites built for conversion, speed, and seamless user experiences.",
    href: "/services/web-development",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2",
  },
  {
    Icon: FileTextIcon,
    name: "SEO & Content Engineering",
    description: "Strategic content creation and SEO optimization that drives organic growth and authority.",
    href: "/services/seo-content",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-3",
  },
  {
    Icon: BarChartIcon,
    name: "Data & Analytics",
    description: "Advanced analytics and data insights that inform strategy and drive measurable business growth.",
    href: "/services/data-analytics",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "lg:col-start-3 lg:col-end-3 lg:row-start-3 lg:row-end-4",
  },
];

function BentoDemo() {
  return (
    <BentoGrid className="lg:grid-rows-4">
      {features.map((feature) => (
        <BentoCard key={feature.name} {...feature} />
      ))}
    </BentoGrid>
  );
}

export { BentoDemo };
