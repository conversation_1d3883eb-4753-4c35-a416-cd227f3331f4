-- Create enum for blog categories
CREATE TYPE blog_category AS ENUM (
  'ai-search',
  'conversion-rate-optimization', 
  'google-ads',
  'youtube-ads',
  'seo',
  'marketing-strategy'
);

-- Create authors table
CREATE TABLE public.authors (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  avatar_url TEXT,
  email TEXT UNIQUE,
  bio TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create blog_articles table
CREATE TABLE public.blog_articles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  slug TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  content TEXT NOT NULL,
  thumbnail_url TEXT,
  published_at TIMESTAMP WITH TIME ZONE,
  reading_time INTEGER,
  category blog_category NOT NULL,
  author_id UUID REFERENCES public.authors(id) ON DELETE SET NULL,
  tags TEXT[],
  featured BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_blog_articles_slug ON public.blog_articles(slug);
CREATE INDEX idx_blog_articles_category ON public.blog_articles(category);
CREATE INDEX idx_blog_articles_published_at ON public.blog_articles(published_at);
CREATE INDEX idx_blog_articles_status ON public.blog_articles(status);
CREATE INDEX idx_blog_articles_featured ON public.blog_articles(featured);
CREATE INDEX idx_blog_articles_tags ON public.blog_articles USING GIN(tags);

-- Enable Row Level Security
ALTER TABLE public.authors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_articles ENABLE ROW LEVEL SECURITY;

-- Create policies for public reading
CREATE POLICY "Anyone can view published articles" 
ON public.blog_articles 
FOR SELECT 
USING (status = 'published');

CREATE POLICY "Anyone can view authors" 
ON public.authors 
FOR SELECT 
USING (true);

-- Create policies for authenticated users to manage content
CREATE POLICY "Authenticated users can manage articles" 
ON public.blog_articles 
FOR ALL 
TO authenticated
USING (true)
WITH CHECK (true);

CREATE POLICY "Authenticated users can manage authors" 
ON public.authors 
FOR ALL 
TO authenticated
USING (true)
WITH CHECK (true);

-- Function to automatically generate slug from title
CREATE OR REPLACE FUNCTION public.generate_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN lower(regexp_replace(regexp_replace(trim(title), '[^a-zA-Z0-9\s-]', '', 'g'), '\s+', '-', 'g'));
END;
$$ LANGUAGE plpgsql;

-- Function to calculate reading time from content
CREATE OR REPLACE FUNCTION public.calculate_reading_time(content TEXT)
RETURNS INTEGER AS $$
DECLARE
  word_count INTEGER;
  reading_time INTEGER;
BEGIN
  -- Remove HTML tags and count words
  word_count := array_length(string_to_array(regexp_replace(content, '<[^>]*>', '', 'g'), ' '), 1);
  -- Assume 200 words per minute reading speed
  reading_time := GREATEST(1, ROUND(word_count / 200.0));
  RETURN reading_time;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to auto-generate slug and reading time
CREATE OR REPLACE FUNCTION public.update_blog_article_meta()
RETURNS TRIGGER AS $$
BEGIN
  -- Auto-generate slug if not provided
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    NEW.slug := public.generate_slug(NEW.title);
  END IF;
  
  -- Auto-calculate reading time
  NEW.reading_time := public.calculate_reading_time(NEW.content);
  
  -- Update timestamp
  NEW.updated_at := now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic slug and reading time generation
CREATE TRIGGER update_blog_article_meta_trigger
BEFORE INSERT OR UPDATE ON public.blog_articles
FOR EACH ROW
EXECUTE FUNCTION public.update_blog_article_meta();

-- Create storage bucket for blog images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('blog-images', 'blog-images', true);

-- Create storage policies for blog images
CREATE POLICY "Anyone can view blog images" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'blog-images');

CREATE POLICY "Authenticated users can upload blog images" 
ON storage.objects 
FOR INSERT 
TO authenticated
WITH CHECK (bucket_id = 'blog-images');

CREATE POLICY "Authenticated users can update blog images" 
ON storage.objects 
FOR UPDATE 
TO authenticated
USING (bucket_id = 'blog-images');

CREATE POLICY "Authenticated users can delete blog images" 
ON storage.objects 
FOR DELETE 
TO authenticated
USING (bucket_id = 'blog-images');

-- Insert default author (you can update this later)
INSERT INTO public.authors (name, email) 
VALUES ('Admin', '<EMAIL>');

-- Function to get published articles with author info
CREATE OR REPLACE FUNCTION public.get_published_articles(
  category_filter blog_category DEFAULT NULL,
  limit_count INTEGER DEFAULT 10,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  slug TEXT,
  title TEXT,
  description TEXT,
  content TEXT,
  thumbnail_url TEXT,
  published_at TIMESTAMP WITH TIME ZONE,
  reading_time INTEGER,
  category blog_category,
  author_name TEXT,
  author_avatar TEXT,
  tags TEXT[],
  featured BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ba.id,
    ba.slug,
    ba.title,
    ba.description,
    ba.content,
    ba.thumbnail_url,
    ba.published_at,
    ba.reading_time,
    ba.category,
    a.name as author_name,
    a.avatar_url as author_avatar,
    ba.tags,
    ba.featured
  FROM public.blog_articles ba
  LEFT JOIN public.authors a ON ba.author_id = a.id
  WHERE ba.status = 'published'
    AND (category_filter IS NULL OR ba.category = category_filter)
  ORDER BY ba.published_at DESC NULLS LAST
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;