import { useState } from 'react';
import { BlogCategory, BLOG_CATEGORIES } from '@/types/blog';
import { createArticle } from '@/lib/blog-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ArticleFormData {
  title: string;
  slug: string;
  description: string;
  content: string;
  thumbnail: string;
  category: BlogCategory | '';
  authorName: string;
  authorAvatar: string;
  authorBio: string;
  tags: string;
  featured: boolean;
}

export function ArticleForm() {
  const [formData, setFormData] = useState<ArticleFormData>({
    title: '',
    slug: '',
    description: '',
    content: '',
    thumbnail: '/placeholder.svg',
    category: '',
    authorName: '<PERSON><PERSON><PERSON>',
    authorAvatar: 'https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images//Yananai%20Chiwuta%20FunnelVision%20CEO.webp',
    authorBio: 'Yananai Chiwuta is the founder and CEO of FunnelVision, a performance-led search marketing agency helping 7–9 figure DTC eCommerce brands turn Google into a predictable, profitable growth engine. FunnelVision blends paid search, YouTube Ads, CRO, and AI-powered discovery strategies to drive scalable acquisition built on strong unit economics. Yananai has helped countless Shopify brands optimize every stage of their customer journey—turning cold traffic into high-margin growth through full-funnel strategy and hands-on execution. He also co-hosts Clicks & Commerce, a podcast for growth-minded DTC operators, and regularly shares tactical audits, case studies, and marketing insights that help brands stay ahead in the evolving world of search. Follow him on <a href="https://linkedin.com/in/yananai" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">LinkedIn</a> to see how top DTC brands are scaling smarter.',
    tags: '',
    featured: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.category) {
      setMessage({ type: 'error', text: 'Please select a category' });
      return;
    }

    setIsSubmitting(true);
    setMessage(null);

    try {
      const articleData = {
        title: formData.title,
        slug: formData.slug,
        description: formData.description,
        content: formData.content,
        thumbnail: formData.thumbnail,
        publishedAt: new Date().toISOString(),
        category: formData.category as BlogCategory,
        author: {
          name: formData.authorName,
          avatar: formData.authorAvatar,
          bio: formData.authorBio
        },
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        featured: formData.featured
      };

      const result = await createArticle(articleData);
      
      if (result) {
        setMessage({ type: 'success', text: `Article "${result.title}" created successfully!` });
        // Reset form
        setFormData({
          title: '',
          slug: '',
          description: '',
          content: '',
          thumbnail: '/placeholder.svg',
          category: '',
          authorName: 'Yananai Chiwuta',
          authorAvatar: 'https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images//Yananai%20Chiwuta%20FunnelVision%20CEO.webp',
          authorBio: 'Yananai Chiwuta is the founder and CEO of FunnelVision, a performance-led search marketing agency helping 7–9 figure DTC eCommerce brands turn Google into a predictable, profitable growth engine. FunnelVision blends paid search, YouTube Ads, CRO, and AI-powered discovery strategies to drive scalable acquisition built on strong unit economics. Yananai has helped countless Shopify brands optimize every stage of their customer journey—turning cold traffic into high-margin growth through full-funnel strategy and hands-on execution. He also co-hosts Clicks & Commerce, a podcast for growth-minded DTC operators, and regularly shares tactical audits, case studies, and marketing insights that help brands stay ahead in the evolving world of search. Follow him on <a href="https://linkedin.com/in/yananai" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">LinkedIn</a> to see how top DTC brands are scaling smarter.',
          tags: '',
          featured: false
        });
      } else {
        setMessage({ type: 'error', text: 'Failed to create article. Please check your Supabase configuration.' });
      }
    } catch (error) {
      console.error('Error creating article:', error);
      setMessage({ type: 'error', text: 'An error occurred while creating the article.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Article</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title and Slug */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleTitleChange(e.target.value)}
                placeholder="Article title"
                required
              />
            </div>
            <div>
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="article-slug"
                required
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of the article"
              rows={3}
              required
            />
          </div>

          {/* Content */}
          <div>
            <Label htmlFor="content">Content * (Markdown supported)</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Full article content in markdown..."
              rows={15}
              required
            />
          </div>

          {/* Category and Thumbnail */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Category *</Label>
              <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as BlogCategory }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {BLOG_CATEGORIES.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="thumbnail">Thumbnail URL</Label>
              <Input
                id="thumbnail"
                value={formData.thumbnail}
                onChange={(e) => setFormData(prev => ({ ...prev, thumbnail: e.target.value }))}
                placeholder="/path/to/image.jpg"
              />
            </div>
          </div>

          {/* Author Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="authorName">Author Name</Label>
              <Input
                id="authorName"
                value={formData.authorName}
                onChange={(e) => setFormData(prev => ({ ...prev, authorName: e.target.value }))}
                placeholder="Author name"
              />
            </div>
            <div>
              <Label htmlFor="authorAvatar">Author Avatar URL</Label>
              <Input
                id="authorAvatar"
                value={formData.authorAvatar}
                onChange={(e) => setFormData(prev => ({ ...prev, authorAvatar: e.target.value }))}
                placeholder="https://example.com/avatar.jpg"
              />
            </div>
          </div>

          {/* Author Bio */}
          <div>
            <Label htmlFor="authorBio">Author Bio (HTML supported)</Label>
            <Textarea
              id="authorBio"
              value={formData.authorBio}
              onChange={(e) => setFormData(prev => ({ ...prev, authorBio: e.target.value }))}
              placeholder="Author biography with HTML links..."
              rows={4}
            />
          </div>

          {/* Tags */}
          <div>
            <Label htmlFor="tags">Tags (comma-separated)</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              placeholder="SEO, Marketing, Strategy"
            />
          </div>

          {/* Featured checkbox */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="featured"
              checked={formData.featured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: !!checked }))}
            />
            <Label htmlFor="featured">Featured article</Label>
          </div>

          {/* Message */}
          {message && (
            <div className={`p-4 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
              {message.text}
            </div>
          )}

          {/* Submit button */}
          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? 'Creating Article...' : 'Create Article'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
