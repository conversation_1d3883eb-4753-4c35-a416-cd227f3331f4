# Image Folders Guide for FunnelVision

This guide shows you exactly where to place different types of images in your project.

## 📁 Folder Structure Created

```
public/
└── images/
    ├── logos/          # FunnelVision branding
    ├── clients/        # Client logos for hero carousel
    ├── case-studies/   # Case study images
    ├── testimonials/   # Customer profile photos
    └── blog/           # Blog article images
```

## 🎨 1. FunnelVision Logo

**Folder:** `public/images/logos/`

**Files to add:**
- `funnelvision-logo.svg` (preferred - scalable)
- `funnelvision-logo.png` (fallback - high resolution)
- `funnelvision-logo-white.svg` (for dark backgrounds)
- `funnelvision-logo-icon.svg` (just the "FV" icon)

**Usage in code:**
```javascript
// In navbar and footer components
<img src="/images/logos/funnelvision-logo.svg" alt="FunnelVision" />
```

**Recommended specs:**
- Format: SVG (preferred) or PNG
- Background: Transparent
- Resolution: Vector (SVG) or 300+ DPI (PNG)
- Variants: Light and dark versions

---

## 🏢 2. Client Logos (Hero Carousel)

**Folder:** `public/images/clients/`

**Files to add:**
- `client-1.svg` or `client-1.png`
- `client-2.svg` or `client-2.png`
- `client-3.svg` or `client-3.png`
- etc.

**Current component:** `src/components/ui/hero-demo.tsx`

**Usage example:**
```javascript
const clientLogos = [
  { src: "/images/clients/shopify.svg", alt: "Shopify" },
  { src: "/images/clients/amazon.svg", alt: "Amazon" },
  { src: "/images/clients/walmart.svg", alt: "Walmart" },
  // Add more clients
];
```

**Recommended specs:**
- Format: SVG (preferred) or PNG
- Background: Transparent
- Size: Consistent height (around 40-60px)
- Style: Monochrome or muted colors for consistency

---

## 📊 3. Case Study Images

**Folder:** `public/images/case-studies/`

**Files to add:**
- `case-study-1-before.jpg`
- `case-study-1-after.jpg`
- `case-study-1-dashboard.jpg`
- `case-study-2-results.jpg`
- etc.

**Current component:** `src/components/ui/case-studies-demo.tsx`

**Usage example:**
```javascript
const caseStudies = [
  {
    title: "E-commerce Brand Optimization",
    image: "/images/case-studies/ecommerce-results.jpg",
    beforeImage: "/images/case-studies/ecommerce-before.jpg",
    afterImage: "/images/case-studies/ecommerce-after.jpg"
  }
];
```

**Recommended specs:**
- Format: JPG or WebP
- Resolution: 1200x800px (3:2 aspect ratio)
- Quality: High (80-90%)
- Content: Screenshots, charts, before/after comparisons

---

## 👥 4. Customer Testimonial Photos

**Folder:** `public/images/testimonials/`

**Files to add:**
- `customer-1.jpg`
- `customer-2.jpg`
- `customer-3.jpg`
- etc.

**Current component:** `src/components/ui/testimonials-demo.tsx`

**Usage example:**
```javascript
const testimonials = [
  {
    name: "John Smith",
    company: "E-commerce Store",
    avatar: "/images/testimonials/john-smith.jpg",
    testimonial: "FunnelVision increased our conversion rate by 300%..."
  }
];
```

**Recommended specs:**
- Format: JPG or WebP
- Size: 200x200px (square)
- Quality: Professional headshots
- Background: Clean, professional
- Style: Consistent lighting and framing

---

## 📝 5. Blog Images

**Folder:** `public/images/blog/`

**Files to add:**
- `article-slug-featured.jpg` (main article image)
- `article-slug-chart.jpg` (charts/graphs)
- `article-slug-screenshot.jpg` (screenshots)
- etc.

**Usage in articles:**
```markdown
![SEO Performance Dashboard](/images/blog/seo-dashboard-results.jpg)
```

**Recommended specs:**
- Format: JPG or WebP
- Resolution: 1200x675px (16:9 aspect ratio)
- Quality: Optimized for web (under 500KB)
- Content: Screenshots, charts, infographics

---

## 🔧 How to Update Components

### 1. Update Hero Client Carousel

Edit `src/components/ui/hero-demo.tsx`:

```javascript
// Replace the placeholder logos with your client logos
const clientLogos = [
  { src: "/images/clients/your-client-1.svg", alt: "Client 1" },
  { src: "/images/clients/your-client-2.svg", alt: "Client 2" },
  // Add all your client logos
];
```

### 2. Update Case Studies

Edit `src/components/ui/case-studies-demo.tsx`:

```javascript
// Replace with your actual case studies
const caseStudies = [
  {
    title: "Your Case Study Title",
    image: "/images/case-studies/your-case-study.jpg",
    description: "Your case study description...",
    results: "300% increase in conversions"
  }
];
```

### 3. Update Testimonials

Edit `src/components/ui/testimonials-demo.tsx`:

```javascript
// Replace with your actual customer testimonials
const testimonials = [
  {
    name: "Customer Name",
    company: "Company Name",
    avatar: "/images/testimonials/customer-photo.jpg",
    testimonial: "Your customer testimonial..."
  }
];
```

### 4. Update Navigation Logo

Edit `src/components/ui/enhanced-navbar.tsx`:

```javascript
// Replace the current logo with your FunnelVision logo
const Logo = () => (
  <a href="/" className="flex items-center space-x-2">
    <img src="/images/logos/funnelvision-logo.svg" alt="FunnelVision" className="h-8" />
  </a>
);
```

---

## 📐 Image Optimization Tips

### File Formats
- **SVG**: Logos and icons (scalable, small file size)
- **WebP**: Photos and complex images (best compression)
- **JPG**: Photos when WebP isn't supported
- **PNG**: Images requiring transparency

### Optimization Tools
- **Online**: TinyPNG, Squoosh.app
- **Local**: ImageOptim (Mac), RIOT (Windows)
- **Automated**: Consider using Cloudinary or similar CDN

### Performance Best Practices
- Compress images before uploading
- Use appropriate dimensions (don't upload 4K images for 200px display)
- Consider lazy loading for below-the-fold images
- Use responsive images with different sizes

---

## 🚀 Quick Start Checklist

1. **✅ Folders Created**: All image folders are ready
2. **📁 Add Your Images**: Place images in appropriate folders
3. **🔧 Update Components**: Modify the demo components with your content
4. **🎨 Test Display**: Check images display correctly
5. **⚡ Optimize**: Compress images for web performance

## 📂 Current Folder Locations

All folders are located at:
```
C:\Users\<USER>\Documents\GitHub\FV\hero-component-kit\public\images\
```

You can now drag and drop your images directly into these folders and update the components to use them!

## 🔗 Alternative: Supabase Storage

For better performance and management, you can also upload images to your Supabase storage:

1. Go to Supabase Dashboard → Storage
2. Upload to appropriate buckets
3. Use the public URLs in your components

This is especially recommended for blog images and frequently changing content.
