-- Migration to add author_bio column to existing blog_articles table
-- Run this if you already have the blog_articles table without the author_bio column

-- Add the author_bio column
ALTER TABLE blog_articles 
ADD COLUMN IF NOT EXISTS author_bio TEXT;

-- Update existing records with the default author bio
UPDATE blog_articles 
SET author_bio = '<PERSON><PERSON><PERSON> is the founder and CEO of FunnelVision, a performance-led search marketing agency helping 7–9 figure DTC eCommerce brands turn Google into a predictable, profitable growth engine. FunnelVision blends paid search, YouTube Ads, CRO, and AI-powered discovery strategies to drive scalable acquisition built on strong unit economics. <PERSON><PERSON><PERSON> has helped countless Shopify brands optimize every stage of their customer journey—turning cold traffic into high-margin growth through full-funnel strategy and hands-on execution. He also co-hosts Clicks & Commerce, a podcast for growth-minded DTC operators, and regularly shares tactical audits, case studies, and marketing insights that help brands stay ahead in the evolving world of search. Follow him on <a href="https://linkedin.com/in/yananai" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">LinkedIn</a> to see how top DTC brands are scaling smarter.'
WHERE author_bio IS NULL;

-- Update author avatar to use the correct image URL
UPDATE blog_articles 
SET author_avatar = 'https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images//Yananai%20Chiwuta%20FunnelVision%20CEO.webp'
WHERE author_avatar = '/placeholder.svg' OR author_avatar IS NULL;
