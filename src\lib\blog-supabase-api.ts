import { BlogArticle, BlogCategory } from '@/types/blog';
import { supabase, dbToAppArticle, appToDbArticle, BlogArticleDB } from '@/lib/supabase';
import { calculateReadingTime } from '@/lib/reading-time';

/**
 * Supabase-powered blog API functions
 */

/**
 * Get all blog articles sorted by publish date (newest first)
 */
export async function getAllArticles(): Promise<BlogArticle[]> {
  try {
    const { data, error } = await supabase
      .from('blog_articles')
      .select('*')
      .order('published_at', { ascending: false });

    if (error) {
      console.error('Error fetching articles:', error);
      return [];
    }

    return data.map(dbToAppArticle);
  } catch (error) {
    console.error('Error in getAllArticles:', error);
    return [];
  }
}

/**
 * Get articles by category
 */
export async function getArticlesByCategory(category: BlogCategory): Promise<BlogArticle[]> {
  try {
    const { data, error } = await supabase
      .from('blog_articles')
      .select('*')
      .eq('category', category)
      .order('published_at', { ascending: false });

    if (error) {
      console.error('Error fetching articles by category:', error);
      return [];
    }

    return data.map(dbToAppArticle);
  } catch (error) {
    console.error('Error in getArticlesByCategory:', error);
    return [];
  }
}

/**
 * Get featured article (most recent featured article)
 */
export async function getFeaturedArticle(): Promise<BlogArticle | null> {
  try {
    const { data, error } = await supabase
      .from('blog_articles')
      .select('*')
      .eq('featured', true)
      .order('published_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error fetching featured article:', error);
      return null;
    }

    if (data && data.length > 0) {
      return dbToAppArticle(data[0]);
    }

    // If no featured article, return the most recent article
    const { data: recentData, error: recentError } = await supabase
      .from('blog_articles')
      .select('*')
      .order('published_at', { ascending: false })
      .limit(1);

    if (recentError || !recentData || recentData.length === 0) {
      return null;
    }

    return dbToAppArticle(recentData[0]);
  } catch (error) {
    console.error('Error in getFeaturedArticle:', error);
    return null;
  }
}

/**
 * Get article by slug
 */
export async function getArticleBySlug(slug: string): Promise<BlogArticle | null> {
  try {
    const { data, error } = await supabase
      .from('blog_articles')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('Error fetching article by slug:', error);
      return null;
    }

    return dbToAppArticle(data);
  } catch (error) {
    console.error('Error in getArticleBySlug:', error);
    return null;
  }
}

/**
 * Get related articles (same category, excluding current article)
 */
export async function getRelatedArticles(currentArticle: BlogArticle, limit: number = 3): Promise<BlogArticle[]> {
  try {
    const { data, error } = await supabase
      .from('blog_articles')
      .select('*')
      .eq('category', currentArticle.category)
      .neq('id', currentArticle.id)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching related articles:', error);
      return [];
    }

    return data.map(dbToAppArticle);
  } catch (error) {
    console.error('Error in getRelatedArticles:', error);
    return [];
  }
}

/**
 * Get recent articles for homepage (excluding featured)
 */
export async function getRecentArticles(limit: number = 4): Promise<BlogArticle[]> {
  try {
    const featured = await getFeaturedArticle();
    
    let query = supabase
      .from('blog_articles')
      .select('*')
      .order('published_at', { ascending: false })
      .limit(limit + 1); // Get one extra in case we need to exclude featured

    if (featured) {
      query = query.neq('id', featured.id);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching recent articles:', error);
      return [];
    }

    return data.slice(0, limit).map(dbToAppArticle);
  } catch (error) {
    console.error('Error in getRecentArticles:', error);
    return [];
  }
}

/**
 * Create a new blog article
 */
export async function createArticle(article: Omit<BlogArticle, 'id' | 'readingTime'>): Promise<BlogArticle | null> {
  try {
    const articleWithReadingTime = {
      ...article,
      readingTime: calculateReadingTime(article.content)
    };

    const dbArticle = appToDbArticle(articleWithReadingTime);

    const { data, error } = await supabase
      .from('blog_articles')
      .insert([dbArticle])
      .select()
      .single();

    if (error) {
      console.error('Error creating article:', error);
      return null;
    }

    return dbToAppArticle(data);
  } catch (error) {
    console.error('Error in createArticle:', error);
    return null;
  }
}

/**
 * Update an existing blog article
 */
export async function updateArticle(id: string, updates: Partial<Omit<BlogArticle, 'id'>>): Promise<BlogArticle | null> {
  try {
    // If content is being updated, recalculate reading time
    if (updates.content) {
      updates.readingTime = calculateReadingTime(updates.content);
    }

    const dbUpdates = appToDbArticle(updates as Partial<BlogArticle>);

    const { data, error } = await supabase
      .from('blog_articles')
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating article:', error);
      return null;
    }

    return dbToAppArticle(data);
  } catch (error) {
    console.error('Error in updateArticle:', error);
    return null;
  }
}

/**
 * Delete a blog article
 */
export async function deleteArticle(id: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('blog_articles')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting article:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteArticle:', error);
    return false;
  }
}

/**
 * Search articles by title, description, and tags
 */
export async function searchArticles(query: string): Promise<BlogArticle[]> {
  try {
    const searchTerm = `%${query.toLowerCase()}%`;
    
    const { data, error } = await supabase
      .from('blog_articles')
      .select('*')
      .or(`title.ilike.${searchTerm},description.ilike.${searchTerm}`)
      .order('published_at', { ascending: false });

    if (error) {
      console.error('Error searching articles:', error);
      return [];
    }

    return data.map(dbToAppArticle);
  } catch (error) {
    console.error('Error in searchArticles:', error);
    return [];
  }
}
