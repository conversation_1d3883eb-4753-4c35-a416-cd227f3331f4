import { createClient } from '@supabase/supabase-js';
import { BlogArticle, BlogCategory } from '@/types/blog';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types for Supabase
export interface BlogArticleDB {
  id: string;
  slug: string;
  title: string;
  description: string;
  content: string;
  thumbnail: string;
  published_at: string;
  reading_time: number;
  category: BlogCategory;
  author_name: string;
  author_avatar?: string;
  author_bio?: string;
  tags?: string[];
  featured: boolean;
  created_at: string;
  updated_at: string;
}

// Convert database article to app article format
export function dbToAppArticle(dbArticle: BlogArticleDB): BlogArticle {
  return {
    id: dbArticle.id,
    slug: dbArticle.slug,
    title: dbArticle.title,
    description: dbArticle.description,
    content: dbArticle.content,
    thumbnail: dbArticle.thumbnail,
    publishedAt: dbArticle.published_at,
    readingTime: dbArticle.reading_time,
    category: dbArticle.category,
    author: {
      name: dbArticle.author_name,
      avatar: dbArticle.author_avatar,
      bio: dbArticle.author_bio
    },
    tags: dbArticle.tags,
    featured: dbArticle.featured
  };
}

// Convert app article to database format
export function appToDbArticle(appArticle: Omit<BlogArticle, 'id'>): Omit<BlogArticleDB, 'id' | 'created_at' | 'updated_at'> {
  return {
    slug: appArticle.slug,
    title: appArticle.title,
    description: appArticle.description,
    content: appArticle.content,
    thumbnail: appArticle.thumbnail,
    published_at: appArticle.publishedAt,
    reading_time: appArticle.readingTime,
    category: appArticle.category,
    author_name: appArticle.author.name,
    author_avatar: appArticle.author.avatar,
    author_bio: appArticle.author.bio,
    tags: appArticle.tags,
    featured: appArticle.featured || false
  };
}
