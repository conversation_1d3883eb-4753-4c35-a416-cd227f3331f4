import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { But<PERSON> } from "@/components/ui/button";
import { MoveRight, BarChart3, Database, TrendingUp, AlertCircle } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainGrid = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">The Challenge</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="p-6 bg-white rounded-card border">
          <Database className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Data Silos</h3>
          <p className="text-muted-foreground">Your data is scattered across multiple platforms, making it impossible to get a complete view of performance.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <AlertCircle className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Poor Decision Making</h3>
          <p className="text-muted-foreground">Without clear insights, you're making marketing decisions based on gut feeling rather than data-driven evidence.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <BarChart3 className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Missed Opportunities</h3>
          <p className="text-muted-foreground">You can't identify what's working, what's not, or where to invest for maximum growth potential.</p>
        </div>
      </div>
    </div>
  </section>
);

const MethodSteps = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Our Method</h2>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">1</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Data Integration</h3>
          <p className="text-muted-foreground">We connect all your data sources into a unified dashboard that provides a complete view of your business.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">2</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Advanced Analytics</h3>
          <p className="text-muted-foreground">We apply statistical analysis and machine learning to uncover insights that drive strategic decisions.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">3</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Actionable Reporting</h3>
          <p className="text-muted-foreground">We deliver clear, actionable reports that show exactly what to do next to improve performance.</p>
        </div>
      </div>
    </div>
  </section>
);





const DataAnalytics = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader 
          title="Data & Analytics" 
          subtitle="Advanced analytics and data insights that inform strategy and drive measurable business growth." 
        />
        <PainGrid />
        <MethodSteps />


        <CTADemo />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default DataAnalytics;
