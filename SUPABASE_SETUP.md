# Supabase Blog Integration Setup Guide

This guide will help you set up Supabase integration for the FunnelVision blog system.

## Prerequisites

1. A Supabase account (sign up at [supabase.com](https://supabase.com))
2. Node.js installed on your system
3. The FunnelVision project cloned and dependencies installed

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `funnelvision-blog` (or your preferred name)
   - **Database Password**: Generate a strong password
   - **Region**: Choose the closest region to your users
5. Click "Create new project"
6. Wait for the project to be created (this may take a few minutes)

## Step 2: Set Up the Database

1. In your Supabase dashboard, go to the **SQL Editor**
2. Copy the contents of `supabase-schema.sql` from this project
3. Paste it into the SQL Editor
4. Click **Run** to execute the schema

This will create:
- `blog_articles` table with all necessary columns
- Indexes for optimal performance
- Row Level Security (RLS) policies
- A sample test article

## Step 3: Get Your API Keys

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://your-project.supabase.co`)
   - **anon public** key (for client-side operations)
   - **service_role** key (for admin operations - keep this secret!)

## Step 4: Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update your `.env` file with your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_anon_key_here
   SUPABASE_SERVICE_KEY=your_service_key_here
   ```

## Step 5: Test the Integration

1. Install the dotenv package for the test script:
   ```bash
   npm install dotenv
   ```

2. Run the test script:
   ```bash
   node scripts/test-supabase-article.js
   ```

3. If successful, you should see:
   ```
   🚀 Testing Supabase article creation...
   📝 Creating new article...
   ✅ Article created successfully!
   🔍 Testing article retrieval...
   ✅ Article retrieved successfully!
   📚 Testing all articles retrieval...
   ✅ All articles retrieved successfully!
   🎉 All tests passed! Supabase integration is working correctly.
   ```

4. Visit your blog at `http://localhost:8080/blog` to see the test article

## Step 6: Verify the Integration

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:8080/blog`
3. You should see articles loading from Supabase
4. Try filtering by categories
5. Click on an article to view the full content

## Adding New Articles

### Method 1: Using the Admin Functions (Programmatic)

```javascript
import { createArticle } from '@/lib/blog-api';

const newArticle = {
  slug: 'my-new-article',
  title: 'My New Article Title',
  description: 'A brief description of the article',
  content: 'Full article content in markdown...',
  thumbnail: '/path/to/thumbnail.jpg',
  publishedAt: new Date().toISOString(),
  category: 'seo', // or any valid category
  author: {
    name: 'Author Name',
    avatar: '/path/to/avatar.jpg'
  },
  tags: ['SEO', 'Marketing'],
  featured: false
};

const result = await createArticle(newArticle);
```

### Method 2: Direct Database Insert

1. Go to your Supabase dashboard
2. Navigate to **Table Editor** → **blog_articles**
3. Click **Insert** → **Insert row**
4. Fill in the required fields:
   - `slug`: URL-friendly version of title
   - `title`: Article title
   - `description`: Brief description
   - `content`: Full article content
   - `thumbnail`: Image URL
   - `category`: One of the valid categories
   - `author_name`: Author's name
   - `tags`: Array of tags (optional)
   - `featured`: Boolean for featured status

## Available Categories

- `ai-search`: AI Search
- `conversion-rate-optimization`: Conversion Rate Optimization
- `google-ads`: Google Ads
- `youtube-ads`: YouTube Ads
- `seo`: SEO
- `marketing-strategy`: Marketing Strategy

## Fallback Behavior

The system is designed to gracefully handle Supabase unavailability:

1. **With Supabase**: Articles are loaded from the database
2. **Without Supabase**: Falls back to static articles in `src/data/blog-articles.ts`
3. **Supabase Error**: Logs warning and uses static fallback

This ensures your blog always works, even if Supabase is temporarily unavailable.

## Security Considerations

1. **Never expose the service key** in client-side code
2. **Use RLS policies** to control data access
3. **Validate input** before inserting articles
4. **Use HTTPS** in production
5. **Regularly update** Supabase client library

## Troubleshooting

### Common Issues

1. **"Missing Supabase configuration" error**
   - Check that your `.env` file has the correct values
   - Ensure environment variables are properly loaded

2. **Database connection errors**
   - Verify your project URL and API keys
   - Check that your Supabase project is active

3. **Articles not appearing**
   - Run the test script to verify database setup
   - Check browser console for errors
   - Verify RLS policies allow public read access

4. **Build errors**
   - Ensure all dependencies are installed: `npm install`
   - Check that TypeScript types are correct

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Review the Supabase dashboard logs
3. Verify your database schema matches `supabase-schema.sql`
4. Test with the provided test script

## Production Deployment

For production deployment:

1. **Environment Variables**: Set up environment variables in your hosting platform
2. **Database Backup**: Set up regular backups in Supabase
3. **Monitoring**: Enable logging and monitoring
4. **Performance**: Consider enabling database connection pooling
5. **Security**: Review and tighten RLS policies as needed

Your blog system is now ready for production use with Supabase! 🎉
