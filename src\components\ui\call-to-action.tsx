import { MoveRight, Phone<PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

function CTA() {
  return (
    <div className="w-full py-20 lg:py-40">
      <div className="container mx-auto">
        <div className="flex flex-col text-center rounded-card p-4 lg:p-14 gap-8 items-center" style={{backgroundColor: '#202124'}}>
          <div className="flex flex-col gap-2">
            <h3 className="text-3xl md:text-5xl tracking-tighter max-w-xl font-regular text-white">
              Turn Intent Into Revenue
            </h3>
            <p className="text-lg leading-relaxed tracking-tight max-w-xl text-white/80">
              Stop guessing what works. Get a data-driven strategy that turns customer intent into margin—at scale.
              Book your Profit-Action Plan and see how we can grow your business.
            </p>
          </div>
          <div className="flex flex-row gap-4">
            <Button className="gap-4 bg-white text-black hover:bg-white/90" onClick={() => window.location.href = '/book-a-call'}>
              Book Profit-Action Plan <PhoneCall className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export { CTA };