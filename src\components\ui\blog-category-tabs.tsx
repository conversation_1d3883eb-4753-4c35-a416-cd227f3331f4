import { useState } from 'react';
import { BlogCategory, BLOG_CATEGORIES } from '@/types/blog';
import { cn } from '@/lib/utils';

interface BlogCategoryTabsProps {
  activeCategory: 'all' | BlogCategory;
  onCategoryChange: (category: 'all' | BlogCategory) => void;
}

export function BlogCategoryTabs({ activeCategory, onCategoryChange }: BlogCategoryTabsProps) {
  const allCategories = [
    { id: 'all' as const, name: 'All Articles', slug: 'all' },
    ...BLOG_CATEGORIES
  ];

  return (
    <div className="w-full mb-12">
      {/* Desktop Tabs */}
      <div className="hidden md:flex flex-wrap gap-2 justify-center">
        {allCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={cn(
              "px-6 py-3 rounded-full text-sm font-medium transition-all duration-200",
              "hover:bg-primary/10 hover:text-primary",
              activeCategory === category.id
                ? "bg-primary text-primary-foreground shadow-md"
                : "bg-muted text-muted-foreground"
            )}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Mobile Dropdown */}
      <div className="md:hidden">
        <select
          value={activeCategory}
          onChange={(e) => onCategoryChange(e.target.value as 'all' | BlogCategory)}
          className="w-full px-4 py-3 rounded-lg border border-border bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
        >
          {allCategories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
