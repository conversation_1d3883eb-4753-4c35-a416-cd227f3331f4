import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { Button } from "@/components/ui/button";
import { ArrowLeft, TrendingDown, Users, ShoppingCart } from "lucide-react";

const CaseStudyHeader = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <Button variant="ghost" className="gap-2 mb-6" onClick={() => window.history.back()}>
          <ArrowLeft className="h-4 w-4" />
          Back to Case Studies
        </Button>
      </div>
      
      <div className="flex items-center gap-4 mb-8">
        <img 
          src="/images/clients/14.png" 
          alt="Bunkie Life" 
          className="h-12 w-auto"
        />
        <div>
          <h1 className="text-4xl md:text-6xl font-bold text-foreground">
            Bunkie Life Case Study
          </h1>
          <p className="text-xl text-muted-foreground mt-2">
            How we increased monthly revenue by 122% through CRO optimization
          </p>
        </div>
      </div>
      
      <div className="aspect-video bg-muted rounded-lg overflow-hidden mb-8">
        <img 
          src="/images/case-studies/case-2.webp" 
          alt="Bunkie Life Case Study" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  </section>
);

const Challenge = () => (
  <section className="py-20 px-4 bg-muted/10">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8">The Challenge</h2>
      <div className="prose prose-lg max-w-none">
        <p className="text-muted-foreground leading-relaxed mb-6">
          Bunkie Life, a premium outdoor living solutions company, had strong traffic but poor conversion rates. 
          Visitors were interested but weren't completing purchases, indicating significant UX and conversion 
          optimization opportunities.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 my-8">
          <div className="p-6 bg-background rounded-lg border">
            <TrendingDown className="h-8 w-8 text-red-500 mb-4" />
            <h3 className="font-semibold mb-2">Low Conversions</h3>
            <p className="text-sm text-muted-foreground">Only 1.2% of visitors were converting to customers</p>
          </div>
          <div className="p-6 bg-background rounded-lg border">
            <Users className="h-8 w-8 text-orange-500 mb-4" />
            <h3 className="font-semibold mb-2">High Bounce Rate</h3>
            <p className="text-sm text-muted-foreground">67% of visitors left without engaging</p>
          </div>
          <div className="p-6 bg-background rounded-lg border">
            <ShoppingCart className="h-8 w-8 text-yellow-500 mb-4" />
            <h3 className="font-semibold mb-2">Cart Abandonment</h3>
            <p className="text-sm text-muted-foreground">78% cart abandonment rate</p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const Solution = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8">Our Solution</h2>
      <div className="prose prose-lg max-w-none">
        <p className="text-muted-foreground leading-relaxed mb-6">
          We implemented a comprehensive CRO and UX optimization strategy focused on reducing friction, 
          improving trust signals, and streamlining the customer journey from discovery to purchase.
        </p>
        
        <div className="space-y-6">
          <div className="border-l-4 border-primary pl-6">
            <h3 className="text-xl font-semibold mb-2">1. User Journey Analysis</h3>
            <p className="text-muted-foreground">
              We conducted extensive user behavior analysis using heatmaps, session recordings, and user 
              testing to identify friction points in the conversion funnel.
            </p>
          </div>
          
          <div className="border-l-4 border-primary pl-6">
            <h3 className="text-xl font-semibold mb-2">2. Trust Signal Enhancement</h3>
            <p className="text-muted-foreground">
              We added customer reviews, security badges, and social proof elements throughout the site 
              to build confidence and reduce purchase anxiety.
            </p>
          </div>
          
          <div className="border-l-4 border-primary pl-6">
            <h3 className="text-xl font-semibold mb-2">3. Checkout Optimization</h3>
            <p className="text-muted-foreground">
              We streamlined the checkout process, reduced form fields, and implemented guest checkout 
              options to minimize cart abandonment.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const Results = () => (
  <section className="py-20 px-4 bg-primary/5">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8 text-center">The Results</h2>
      <div className="grid md:grid-cols-3 gap-8 mb-8">
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">122%</div>
          <p className="text-muted-foreground">Increase in monthly revenue</p>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">73%</div>
          <p className="text-muted-foreground">Improvement in conversion rate</p>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">58%</div>
          <p className="text-muted-foreground">Reduction in cart abandonment</p>
        </div>
      </div>
      
      <div className="bg-background rounded-card p-8 border">
        <blockquote className="text-lg italic text-center">
          "Excellent experience with Yananai and the whole team. Insightful, helpful and patient group of people. 
          The revenue increase was beyond our expectations."
        </blockquote>
        <div className="text-center mt-4">
          <p className="font-semibold">David Fraser</p>
          <p className="text-sm text-muted-foreground">CEO & Founder, Bunkie Life</p>
        </div>
      </div>
    </div>
  </section>
);

const BunkieLife = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <CaseStudyHeader />
        <Challenge />
        <Solution />
        <Results />
        <CTADemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default BunkieLife;
