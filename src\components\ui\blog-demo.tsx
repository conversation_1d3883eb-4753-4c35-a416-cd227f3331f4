import { useState, useEffect } from "react";
import { MoveRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getRecentArticles } from "@/lib/blog-api";
import { BlogCard } from "@/components/ui/blog-card";
import { type BlogArticle } from "@/types/blog";

function BlogDemo() {
  const [recentArticles, setRecentArticles] = useState<BlogArticle[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadArticles = async () => {
      try {
        const articles = await getRecentArticles(4);
        setRecentArticles(articles);
      } catch (error) {
        console.error('Error loading recent articles:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticles();
  }, []);

  return (
    <div className="w-full">
      <div className="container mx-auto flex flex-col gap-14">
        <div className="flex w-full flex-col sm:flex-row sm:justify-between sm:items-center gap-8">
          <h4 className="text-3xl md:text-5xl tracking-tighter max-w-xl font-regular">
            Latest articles
          </h4>
          <Button className="gap-4" onClick={() => window.location.href = '/blog'}>
            View all articles <MoveRight className="w-4 h-4" />
          </Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {loading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="bg-muted rounded-md aspect-video animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-3 bg-muted rounded w-2/3 animate-pulse" />
              </div>
            ))
          ) : (
            recentArticles.map((article) => (
              <BlogCard key={article.id} article={article} />
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export { BlogDemo };
