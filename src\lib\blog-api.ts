import { BlogArticle, BlogCategory } from '@/types/blog';
import { ALL_ARTICLES } from '@/data/blog-articles';
import * as SupabaseAPI from '@/lib/blog-supabase-api';

// Check if Supabase is configured
const isSupabaseConfigured = () => {
  return !!(import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY);
};

/**
 * Get all blog articles sorted by publish date (newest first)
 * Uses Supabase if configured, falls back to static data
 */
export async function getAllArticles(): Promise<BlogArticle[]> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.getAllArticles();
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  return ALL_ARTICLES.sort((a, b) =>
    new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );
}

/**
 * Get articles by category
 */
export async function getArticlesByCategory(category: BlogCategory): Promise<BlogArticle[]> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.getArticlesByCategory(category);
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  const allArticles = await getAllArticles();
  return allArticles.filter(article => article.category === category);
}

/**
 * Get featured article (most recent featured article)
 */
export async function getFeaturedArticle(): Promise<BlogArticle | null> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.getFeaturedArticle();
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  const allArticles = await getAllArticles();
  const featuredArticles = allArticles.filter(article => article.featured);
  return featuredArticles.length > 0 ? featuredArticles[0] : allArticles[0];
}

/**
 * Get article by slug
 */
export async function getArticleBySlug(slug: string): Promise<BlogArticle | null> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.getArticleBySlug(slug);
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  return ALL_ARTICLES.find(article => article.slug === slug) || null;
}

/**
 * Get related articles (same category, excluding current article)
 */
export async function getRelatedArticles(currentArticle: BlogArticle, limit: number = 3): Promise<BlogArticle[]> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.getRelatedArticles(currentArticle, limit);
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  const allArticles = await getAllArticles();
  return allArticles
    .filter(article =>
      article.category === currentArticle.category &&
      article.id !== currentArticle.id
    )
    .slice(0, limit);
}

/**
 * Get recent articles for homepage (excluding featured)
 */
export async function getRecentArticles(limit: number = 4): Promise<BlogArticle[]> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.getRecentArticles(limit);
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  const featured = await getFeaturedArticle();
  const allArticles = await getAllArticles();
  return allArticles
    .filter(article => article.id !== featured?.id)
    .slice(0, limit);
}

/**
 * Search articles by title and description
 */
export async function searchArticles(query: string): Promise<BlogArticle[]> {
  if (isSupabaseConfigured()) {
    try {
      return await SupabaseAPI.searchArticles(query);
    } catch (error) {
      console.warn('Supabase failed, falling back to static data:', error);
    }
  }

  // Fallback to static data
  const searchTerm = query.toLowerCase();
  const allArticles = await getAllArticles();
  return allArticles.filter(article =>
    article.title.toLowerCase().includes(searchTerm) ||
    article.description.toLowerCase().includes(searchTerm) ||
    article.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

// Export Supabase-specific functions for admin use
export const createArticle = SupabaseAPI.createArticle;
export const updateArticle = SupabaseAPI.updateArticle;
export const deleteArticle = SupabaseAPI.deleteArticle;
