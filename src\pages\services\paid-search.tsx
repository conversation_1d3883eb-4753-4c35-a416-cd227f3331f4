import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { Button } from "@/components/ui/button";
import { MoveRight, DollarSign, Target, TrendingDown, Zap } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainGrid = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">The Challenge</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="p-6 bg-white rounded-card border">
          <DollarSign className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Rising Costs</h3>
          <p className="text-muted-foreground">CPCs keep climbing while conversion rates stagnate, squeezing your margins and limiting growth potential.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <Target className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Poor Targeting</h3>
          <p className="text-muted-foreground">Generic campaigns waste budget on unqualified clicks instead of reaching high-intent customers ready to buy.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <TrendingDown className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Campaign Complexity</h3>
          <p className="text-muted-foreground">Managing multiple platforms, audiences, and ad variations becomes overwhelming without strategic orchestration.</p>
        </div>
      </div>
    </div>
  </section>
);

const MethodSteps = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Our Method</h2>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">1</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Intent-Based Strategy</h3>
          <p className="text-muted-foreground">We map customer intent signals to create highly targeted campaigns that capture demand at the perfect moment.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">2</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Cross-Platform Orchestration</h3>
          <p className="text-muted-foreground">We coordinate campaigns across Google, Microsoft, Meta, and emerging platforms for maximum reach and efficiency.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">3</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Performance Optimization</h3>
          <p className="text-muted-foreground">We continuously optimize bids, audiences, and creative to maximize ROAS and scale profitable growth.</p>
        </div>
      </div>
    </div>
  </section>
);





const PaidSearch = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader 
          title="Paid Search Orchestration" 
          subtitle="Strategic campaigns that turn customer intent into profitable conversions at scale." 
        />
        <PainGrid />
        <MethodSteps />


        <CTADemo />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default PaidSearch;
