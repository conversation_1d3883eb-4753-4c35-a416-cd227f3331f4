import { useState, useEffect } from 'react';
import { YouTubeVideoCard } from './youtube-video-card';
import { getLatestVideos, YouTubeVideo } from '@/lib/youtube-api';
import { Skeleton } from '@/components/ui/skeleton';

export function ConversionClinicSection() {
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const clinicVideos = await getLatestVideos('conversion-clinic', 3);
        setVideos(clinicVideos);
      } catch (error) {
        console.error('Error loading Conversion Clinic videos:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);

  if (loading) {
    return (
      <div className="w-full">
        <div className="container mx-auto flex flex-col gap-14">
          <div className="flex w-full flex-col sm:flex-row sm:justify-between sm:items-center gap-8">
            <h4 className="text-3xl md:text-5xl tracking-tighter max-w-xl font-regular">
              Conversion Clinic
            </h4>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="aspect-video rounded-lg" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="w-full">
        <div className="container mx-auto flex flex-col gap-14">
          <div className="flex w-full flex-col sm:flex-row sm:justify-between sm:items-center gap-8">
            <h4 className="text-3xl md:text-5xl tracking-tighter max-w-xl font-regular">
              Conversion Clinic
            </h4>
          </div>
          <div className="text-center py-8 text-muted-foreground">
            No videos found for Conversion Clinic
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="container mx-auto flex flex-col gap-14">
        <div className="flex w-full flex-col sm:flex-row sm:justify-between sm:items-center gap-8">
          <h4 className="text-3xl md:text-5xl tracking-tighter max-w-xl font-regular">
            Conversion Clinic
          </h4>
        </div>

        {/* Desktop and Tablet Grid */}
        <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {videos.map((video) => (
            <YouTubeVideoCard key={video.id} video={video} />
          ))}
        </div>

        {/* Mobile Horizontal Scroll */}
        <div className="sm:hidden">
          <div className="overflow-x-auto scrollbar-hide">
            <div className="flex gap-8 pb-4">
              {videos.map((video) => (
                <div key={video.id} className="flex-none w-80">
                  <YouTubeVideoCard video={video} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
