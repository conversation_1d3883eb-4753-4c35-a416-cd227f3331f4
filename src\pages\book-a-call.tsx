import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { SEOHead } from "@/components/ui/seo-head";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, Clock, Users, Zap, Calendar } from "lucide-react";

const BookingHeader = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        Schedule Your Profit-Action Plan
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
        Get a custom strategy session where we'll analyze your current marketing performance and 
        create a roadmap to turn customer intent into profitable growth.
      </p>
      <p className="text-lg text-muted-foreground max-w-xl mx-auto">
        In 45 minutes, we'll identify your biggest opportunities and give you actionable steps 
        to implement immediately—whether you work with us or not.
      </p>
    </div>
  </section>
);

const WhatToExpect = () => (
  <section className="py-20 px-4 bg-muted/10">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">What to Expect</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <Users className="h-8 w-8 text-primary-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-4">Current State Analysis</h3>
          <p className="text-muted-foreground">We'll review your current marketing performance and identify gaps</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <Zap className="h-8 w-8 text-primary-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-4">Opportunity Mapping</h3>
          <p className="text-muted-foreground">We'll identify your biggest growth opportunities and quick wins</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-8 w-8 text-primary-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-4">Action Plan</h3>
          <p className="text-muted-foreground">You'll leave with a clear roadmap and next steps to implement</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <Clock className="h-8 w-8 text-primary-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-4">45 Minutes</h3>
          <p className="text-muted-foreground">Focused, high-value session designed to maximize your time</p>
        </div>
      </div>
    </div>
  </section>
);

const CalendlySection = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold mb-4">Book Your Session</h2>
        <p className="text-muted-foreground">Choose a time that works for you</p>
      </div>
      
      <div id="calendly-placeholder" className="h-[700px] bg-muted/20 rounded-md flex items-center justify-center border-2 border-dashed border-muted-foreground/20">
        <div className="text-center">
          <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Calendly Embed Placeholder</h3>
          <p className="text-muted-foreground">The Calendly booking widget will be embedded here</p>
        </div>
      </div>
    </div>
  </section>
);

const FAQ = () => (
  <section className="py-20 px-4 bg-muted/10">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
      <div className="space-y-8">
        <div className="border-l-4 border-primary pl-6">
          <h3 className="text-xl font-semibold mb-2">Is this really free?</h3>
          <p className="text-muted-foreground">
            Yes, completely free. We believe in providing value upfront. You'll get actionable insights 
            regardless of whether you decide to work with us.
          </p>
        </div>
        
        <div className="border-l-4 border-primary pl-6">
          <h3 className="text-xl font-semibold mb-2">What should I prepare?</h3>
          <p className="text-muted-foreground">
            Have access to your current marketing data (Google Analytics, ad accounts, etc.) and be 
            ready to discuss your business goals and challenges.
          </p>
        </div>
        
        <div className="border-l-4 border-primary pl-6">
          <h3 className="text-xl font-semibold mb-2">Who will I be speaking with?</h3>
          <p className="text-muted-foreground">
            You'll speak directly with one of our senior strategists who has experience in your industry 
            and can provide immediate, actionable insights.
          </p>
        </div>
        
        <div className="border-l-4 border-primary pl-6">
          <h3 className="text-xl font-semibold mb-2">What if I'm not ready to make changes?</h3>
          <p className="text-muted-foreground">
            That's perfectly fine. The session is designed to give you clarity and a roadmap you can 
            implement when you're ready, at your own pace.
          </p>
        </div>
      </div>
    </div>
  </section>
);

const BookACall = () => {
  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="Schedule Your Profit-Action Plan | FunnelVision"
        description="Get a custom strategy session where we'll analyze your current marketing performance and create a roadmap to turn customer intent into profitable growth."
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <BookingHeader />
        <WhatToExpect />
        <CalendlySection />
        <FAQ />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default BookACall;
