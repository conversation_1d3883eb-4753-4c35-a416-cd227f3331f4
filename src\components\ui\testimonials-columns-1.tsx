"use client";
import React from "react";
import { motion } from "motion/react";


export const TestimonialsColumn = (props: {
  className?: string;
  testimonials: typeof testimonials;
  duration?: number;
}) => {
  return (
    <div className={props.className}>
      <motion.div
        animate={{
          translateY: "-50%",
        }}
        transition={{
          duration: props.duration || 10,
          repeat: Infinity,
          ease: "linear",
          repeatType: "loop",
        }}
        className="flex flex-col gap-6 pb-6 bg-background"
      >
        {[
          ...new Array(2).fill(0).map((_, index) => (
            <React.Fragment key={index}>
              {props.testimonials.map(({ text, image, name, role }, i) => (
                <div className="p-10 rounded-3xl border shadow-lg shadow-primary/10 max-w-xs w-full bg-white" key={i}>
                  <div>{text}</div>
                  <div className="flex items-center gap-2 mt-5">
                    <img
                      width={40}
                      height={40}
                      src={image}
                      alt={name}
                      className="h-10 w-10 rounded-full"
                    />
                    <div className="flex flex-col">
                      <div className="font-medium tracking-tight leading-5">{name}</div>
                      <div className="leading-5 opacity-60 tracking-tight">{role}</div>
                    </div>
                  </div>
                </div>
              ))}
            </React.Fragment>
          )),
        ]}
      </motion.div>
    </div>
  );
};

const testimonials = [
  {
    text: "Excellent experience with Yananai and the whole team. Insightful, helpful and patient group of people.",
    image: "/images/testimonials/t1.webp",
    name: "David Fraser",
    role: "CEO & Founder - Bunkie Life",
  },
   {
    text: "Yananai has a sixth sense for finding growth opportunities. He spotted gaps in our strategy no one else saw and helped us scale BEFORE increasing our budget.",
    image: "/images/testimonials/t2.webp",
    name: "Josh Hill",
    role: "Co-Founder - Dirt",
  },
  {
    text: "Every recommendation felt tailored to our business, and every week we saw measurable improvements.",
    image: "/images/testimonials/t3.webp",
    name: "Giselle",
    role: "Marketing Director - Boulies",
  },
  {
    text: "I don't usually leave testimonials, but the results were too good not to share. This was our most profitable Q4 yet. Hire him if you want to win.",
    image: "/images/testimonials/t4.webp",
    name: "Jared",
    role: "CEO & Founder - Kodiak",
  },
  {
    text: "I was skeptical about switching to Google Ads, but this team made it painless. No hand-holding, no fluff—just results.",
    image: "/images/testimonials/t5.webp",
    name: "Roger H",
    role: "Owner - Jewellery Brand",
  },
    {
    text: "Google Ads was always a headache until FunnelVision took over. We're now seeing consistent growth every quarter.",
    image: "/images/testimonials/t6.webp",
    name: "Liam",
    role: "VP Marketing - Adversa",
  },
    {
    text: "FunnelVision are not just an extension of your marketing team as a paid search agency, but they are collaborative, proactive partners.",
    image: "/images/testimonials/t7.webp",
    name: "Ayanda",
    role: "Head of Demand Generation",
  },
  {
    text: "Our CAC dropped overnight, and our revenue soared. There is now more synergy between marketing and sales efforts.",
    image: "/images/testimonials/t8.webp",
    name: "Peeter Kuum",
    role: "Sales Director - Wermo",
  },
    {
    text: "Take it from me: they care. They really do. Highly recommend, that's all I can say!",
    image: "/images/testimonials/t9.webp",
    name: "Priscilla Vasquez",
    role: "Ecommerce & Digital Lead - Florent",
  },
];