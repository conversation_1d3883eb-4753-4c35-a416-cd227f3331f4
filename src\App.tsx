import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Blog from "./pages/Blog";
import BlogArticle from "./pages/BlogArticle";
import NotFound from "./pages/NotFound";
import AIDiscovery from "./pages/services/ai-discovery";
import PaidSearch from "./pages/services/paid-search";
import CroUx from "./pages/services/cro-ux";
import WebDevelopment from "./pages/services/web-development";
import SeoContent from "./pages/services/seo-content";
import DataAnalytics from "./pages/services/data-analytics";
import Boulies from "./pages/case-studies/boulies";
import BunkieLife from "./pages/case-studies/bunkie-life";
import Kodiak from "./pages/case-studies/kodiak";
import BookACall from "./pages/book-a-call";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/blog/:slug" element={<BlogArticle />} />
          <Route path="/services/ai-discovery" element={<AIDiscovery />} />
          <Route path="/services/paid-search" element={<PaidSearch />} />
          <Route path="/services/cro-ux" element={<CroUx />} />
          <Route path="/services/web-development" element={<WebDevelopment />} />
          <Route path="/services/seo-content" element={<SeoContent />} />
          <Route path="/services/data-analytics" element={<DataAnalytics />} />
          <Route path="/case-studies/boulies" element={<Boulies />} />
          <Route path="/case-studies/bunkie-life" element={<BunkieLife />} />
          <Route path="/case-studies/kodiak" element={<Kodiak />} />
          <Route path="/book-a-call" element={<BookACall />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
