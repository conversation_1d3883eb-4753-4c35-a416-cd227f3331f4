import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>t, TrendingUp, Zap, Target } from "lucide-react";

const CaseStudyHeader = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <Button variant="ghost" className="gap-2 mb-6" onClick={() => window.history.back()}>
          <ArrowLeft className="h-4 w-4" />
          Back to Case Studies
        </Button>
      </div>
      
      <div className="flex items-center gap-4 mb-8">
        <img 
          src="/images/clients/13.svg" 
          alt="Kodiak" 
          className="h-12 w-auto"
        />
        <div>
          <h1 className="text-4xl md:text-6xl font-bold text-foreground">
            Kodiak Case Study
          </h1>
          <p className="text-xl text-muted-foreground mt-2">
            How we increased revenue 8.4X in 12 months through strategic growth
          </p>
        </div>
      </div>
      
      <div className="aspect-video bg-muted rounded-lg overflow-hidden mb-8">
        <img 
          src="/images/case-studies/case-3.webp" 
          alt="Kodiak Case Study" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  </section>
);

const Challenge = () => (
  <section className="py-20 px-4 bg-muted/10">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8">The Challenge</h2>
      <div className="prose prose-lg max-w-none">
        <p className="text-muted-foreground leading-relaxed mb-6">
          Kodiak, an innovative outdoor gear company, was struggling to scale their business beyond their 
          initial customer base. They needed a comprehensive growth strategy that would dramatically increase 
          revenue while maintaining profitability.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 my-8">
          <div className="p-6 bg-background rounded-lg border">
            <TrendingUp className="h-8 w-8 text-red-500 mb-4" />
            <h3 className="font-semibold mb-2">Stagnant Growth</h3>
            <p className="text-sm text-muted-foreground">Revenue had plateaued for 8 months</p>
          </div>
          <div className="p-6 bg-background rounded-lg border">
            <Target className="h-8 w-8 text-orange-500 mb-4" />
            <h3 className="font-semibold mb-2">Limited Reach</h3>
            <p className="text-sm text-muted-foreground">Struggling to reach new customer segments</p>
          </div>
          <div className="p-6 bg-background rounded-lg border">
            <Zap className="h-8 w-8 text-yellow-500 mb-4" />
            <h3 className="font-semibold mb-2">Inefficient Scaling</h3>
            <p className="text-sm text-muted-foreground">Previous growth attempts were unprofitable</p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const Solution = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8">Our Solution</h2>
      <div className="prose prose-lg max-w-none">
        <p className="text-muted-foreground leading-relaxed mb-6">
          We implemented a comprehensive growth strategy combining paid search orchestration, SEO optimization, 
          conversion rate optimization, and strategic content marketing to create multiple revenue streams.
        </p>
        
        <div className="space-y-6">
          <div className="border-l-4 border-primary pl-6">
            <h3 className="text-xl font-semibold mb-2">1. Multi-Channel Strategy</h3>
            <p className="text-muted-foreground">
              We developed an integrated approach across paid search, SEO, social media, and email marketing 
              to maximize reach and create synergies between channels.
            </p>
          </div>
          
          <div className="border-l-4 border-primary pl-6">
            <h3 className="text-xl font-semibold mb-2">2. Customer Segmentation</h3>
            <p className="text-muted-foreground">
              We identified and targeted new customer segments with tailored messaging and offers, 
              expanding beyond their original market.
            </p>
          </div>
          
          <div className="border-l-4 border-primary pl-6">
            <h3 className="text-xl font-semibold mb-2">3. Conversion Optimization</h3>
            <p className="text-muted-foreground">
              We optimized every step of the customer journey, from initial awareness through purchase 
              and retention, to maximize lifetime value.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const Results = () => (
  <section className="py-20 px-4 bg-primary/5">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8 text-center">The Results</h2>
      <div className="grid md:grid-cols-3 gap-8 mb-8">
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">8.4x</div>
          <p className="text-muted-foreground">Revenue increase in 12 months</p>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">340%</div>
          <p className="text-muted-foreground">Growth in customer base</p>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">156%</div>
          <p className="text-muted-foreground">Improvement in profit margins</p>
        </div>
      </div>
      
      <div className="bg-background rounded-card p-8 border">
        <blockquote className="text-lg italic text-center">
          "I don't usually leave testimonials, but the results were too good not to share. This was our most 
          profitable year yet. Hire them if you want to win."
        </blockquote>
        <div className="text-center mt-4">
          <p className="font-semibold">Jared</p>
          <p className="text-sm text-muted-foreground">CEO & Founder, Kodiak</p>
        </div>
      </div>
    </div>
  </section>
);

const Kodiak = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <CaseStudyHeader />
        <Challenge />
        <Solution />
        <Results />
        <CTADemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default Kodiak;
