# Security Guide for YouTube Integration

## Overview
This document outlines the security measures implemented to protect your YouTube API key and prevent abuse.

## Security Features Implemented

### 1. Rate Limiting
- **Limit**: Maximum 10 API requests per minute
- **Purpose**: Prevents API quota exhaustion and abuse
- **Behavior**: When limit exceeded, falls back to cached or mock data

### 2. Request Caching
- **Duration**: 5 minutes per cache entry
- **Purpose**: Reduces API calls and improves performance
- **Storage**: In-memory cache (resets on page reload)

### 3. Input Validation
- **maxResults**: Automatically clamped between 1-50
- **Purpose**: Prevents excessive data requests

### 4. Request Timeout
- **Duration**: 10 seconds
- **Purpose**: Prevents hanging requests

### 5. Fallback System
- **Primary**: Live YouTube API data
- **Secondary**: Cached data (if available)
- **Tertiary**: Mock data (always available)

### 6. User-Agent Header
- **Value**: "FunnelVision-Website/1.0"
- **Purpose**: Identifies legitimate requests from your website

## API Key Security Best Practices

### ✅ DO:
1. **Restrict your API key** in Google Cloud Console:
   - Go to Credentials → Edit API Key
   - Under "API restrictions", select "Restrict key"
   - Choose "YouTube Data API v3" only

2. **Set HTTP referrer restrictions**:
   - Add your domain: `https://yourdomain.com/*`
   - Add localhost for development: `http://localhost:*`

3. **Monitor API usage** in Google Cloud Console
4. **Regenerate keys** if compromised
5. **Keep the .env file** in .gitignore

### ❌ DON'T:
1. **Never commit** API keys to version control
2. **Don't share** API keys in public forums
3. **Don't use** the same key for multiple projects
4. **Don't set** overly broad restrictions

## Environment Variables

### Required
```bash
VITE_YOUTUBE_API_KEY=your_api_key_here
```

### Optional (for additional security)
```bash
VITE_API_RATE_LIMIT=10          # Requests per minute (default: 10)
VITE_API_CACHE_DURATION=300000  # Cache duration in ms (default: 5 minutes)
VITE_API_TIMEOUT=10000          # Request timeout in ms (default: 10 seconds)
```

## Monitoring and Alerts

### Google Cloud Console
1. Go to APIs & Services → Dashboard
2. Monitor YouTube Data API v3 usage
3. Set up billing alerts if needed

### Signs of Potential Abuse
- Sudden spike in API usage
- Quota exceeded errors
- Unusual traffic patterns

## Incident Response

### If API Key is Compromised:
1. **Immediately disable** the key in Google Cloud Console
2. **Generate a new key** with proper restrictions
3. **Update your .env file** with the new key
4. **Review access logs** for suspicious activity
5. **Deploy the updated key** to production

### If Quota is Exceeded:
1. Check the rate limiting is working
2. Review cache duration settings
3. Consider increasing quota in Google Cloud Console
4. Implement additional caching layers if needed

## Development vs Production

### Development
- Uses mock data when no API key is provided
- Localhost referrer restrictions
- Lower rate limits for testing

### Production
- Requires valid API key
- Domain-specific referrer restrictions
- Production-optimized cache settings

## Contact
For security concerns or questions, please review this document and the implementation in `src/lib/youtube-api.ts`.
