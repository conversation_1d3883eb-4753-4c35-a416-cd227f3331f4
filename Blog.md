# Blog System Documentation

## Overview
This project uses a Supabase-powered blog system with automatic content management features including slug generation, reading time calculation, and image storage.

## Database Schema

### Tables

#### `authors`
- `id` (UUID, auto-generated)
- `name` (text, required)
- `email` (text, optional)
- `avatar_url` (text, optional)
- `bio` (text, optional)
- `created_at` / `updated_at` (timestamps)

#### `blog_articles`
- `id` (UUID, auto-generated)
- `title` (text, required)
- `slug` (text, auto-generated from title)
- `description` (text, required)
- `content` (text, required - supports HTML/Markdown)
- `thumbnail_url` (text, optional)
- `category` (enum: ai-search, conversion-rate-optimization, google-ads, youtube-ads, seo, marketing-strategy)
- `author_id` (UUID, references authors table)
- `tags` (text array, optional)
- `featured` (boolean, default false)
- `status` (text, default 'draft')
- `published_at` (timestamp, required for published articles)
- `reading_time` (integer, auto-calculated)
- `created_at` / `updated_at` (timestamps)

## Automatic Features

### Slug Generation
- Automatically creates URL-friendly slugs from article titles
- Removes special characters and converts spaces to hyphens
- Example: "Complete Guide to YouTube Ads" → "complete-guide-to-youtube-ads"

### Reading Time Calculation
- Automatically calculates reading time based on content word count
- Assumes 200 words per minute reading speed
- Strips HTML tags before counting words

### Image Storage
- Uses Supabase Storage bucket: `blog-images`
- Public bucket for easy access
- Recommended folder structure:
  - `thumbnails/` - Article thumbnail images
  - `content/` - Images used within article content

## Publishing Workflow

### 1. Prepare Content
Create your article following this structure:
```json
{
  "title": "Your Article Title",
  "description": "Brief description for SEO and previews",
  "content": "Full article content (HTML/Markdown supported)",
  "category": "one-of-the-enum-values",
  "thumbnail_url": "https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/thumbnails/filename.jpg",
  "author_id": "author-uuid",
  "tags": ["tag1", "tag2", "tag3"],
  "featured": false
}
```

### 2. Upload Images
1. Go to [Supabase Storage](https://supabase.com/dashboard/project/vnhswtyhvrhknuvvzxse/storage/buckets)
2. Navigate to `blog-images` bucket
3. Upload images to appropriate folders:
   - Thumbnails: `thumbnails/descriptive-name.jpg`
   - Content images: `content/article-slug-image-name.jpg`

### 3. Insert Article (Draft)
```sql
INSERT INTO blog_articles (
  title, description, content, category, thumbnail_url, author_id, tags, status
) VALUES (
  'Your Title',
  'Your description',
  'Your content with <img src="https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/content/image.jpg" alt="Description" />',
  'your-category',
  'https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/thumbnails/thumb.jpg',
  'author-uuid',
  ARRAY['tag1', 'tag2'],
  'draft'
);
```

### 4. Publish Article
```sql
UPDATE blog_articles 
SET status = 'published', published_at = now() 
WHERE slug = 'your-article-slug';
```

## Image Naming Convention

### Thumbnails
Format: `thumbnails/[category]-[brief-title]-[date].jpg`
Example: `thumbnails/youtube-ads-optimization-guide-2024-01.jpg`

### Content Images
Format: `content/[article-slug]-[image-description].jpg`
Example: `content/youtube-ads-guide-dashboard-screenshot.jpg`

## Content Formatting Guidelines

### HTML in Content
- Use semantic HTML tags: `<h2>`, `<h3>`, `<p>`, `<ul>`, `<ol>`, `<blockquote>`
- Images: `<img src="storage-url" alt="descriptive-text" />`
- Links: `<a href="url" target="_blank">link text</a>`

### Categories Available
- `ai-search` - AI and search optimization
- `conversion-rate-optimization` - CRO strategies
- `google-ads` - Google Ads campaigns
- `youtube-ads` - YouTube advertising
- `seo` - Search engine optimization
- `marketing-strategy` - General marketing

## Row Level Security (RLS)

### Public Access
- Anyone can view published articles (`status = 'published'`)
- Anyone can view author information

### Authenticated Access
- Authenticated users can create, update, delete articles
- Authenticated users can manage author profiles

## Development Integration

### Frontend Components
- `BlogDemo` - Homepage blog section
- `BlogCard` - Individual article cards
- `Blog` page - Full blog listing
- `BlogArticle` page - Individual article view

### API Functions
- `getAllArticles()` - Get all articles sorted by date
- `getArticlesByCategory()` - Filter by category
- `getFeaturedArticle()` - Get most recent featured article
- `getArticleBySlug()` - Get specific article
- `getRelatedArticles()` - Get same-category articles
- `searchArticles()` - Search by title/description/tags

### Supabase Integration
```typescript
import { supabase } from "@/integrations/supabase/client";

// Example: Fetch published articles
const { data: articles } = await supabase
  .from('blog_articles')
  .select(`
    *,
    authors (name, avatar_url)
  `)
  .eq('status', 'published')
  .order('published_at', { ascending: false });
```

## Quick Commands

### View all articles
```sql
SELECT title, slug, status, published_at FROM blog_articles ORDER BY created_at DESC;
```

### Publish a draft
```sql
UPDATE blog_articles SET status = 'published', published_at = now() WHERE slug = 'your-slug';
```

### Make article featured
```sql
UPDATE blog_articles SET featured = true WHERE slug = 'your-slug';
```

### View storage usage
Go to: https://supabase.com/dashboard/project/vnhswtyhvrhknuvvzxse/storage/buckets

## Troubleshooting

### Article not showing on frontend
1. Check status is 'published'
2. Verify published_at is set
3. Ensure RLS policies allow access

### Images not loading
1. Verify bucket is public
2. Check image URLs are correct
3. Ensure images are uploaded to correct bucket

### Slug conflicts
- Slugs are auto-generated and should be unique
- Manual override possible by setting slug field before insert