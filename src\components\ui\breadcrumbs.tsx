import { ChevronRight } from 'lucide-react';
import { BLOG_CATEGORIES, type BlogCategory } from '@/types/blog';

interface BreadcrumbsProps {
  category?: BlogCategory;
  articleTitle?: string;
}

export function Breadcrumbs({ category, articleTitle }: BreadcrumbsProps) {
  const categoryInfo = category ? BLOG_CATEGORIES.find(cat => cat.id === category) : null;

  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
      {/* Home */}
      <a 
        href="/" 
        className="hover:text-primary transition-colors"
      >
        Home
      </a>
      
      <ChevronRight className="h-4 w-4" />
      
      {/* Blog */}
      <a 
        href="/blog" 
        className="hover:text-primary transition-colors"
      >
        Blog
      </a>
      
      {/* Category (if provided) */}
      {categoryInfo && (
        <>
          <ChevronRight className="h-4 w-4" />
          <a 
            href={`/blog?category=${category}`}
            className="hover:text-primary transition-colors"
          >
            {categoryInfo.name}
          </a>
        </>
      )}
      
      {/* Article Title (if provided) */}
      {articleTitle && (
        <>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground font-medium truncate max-w-xs">
            {articleTitle}
          </span>
        </>
      )}
    </nav>
  );
}
