import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { SEOHead, createServiceSchema } from "@/components/ui/seo-head";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { Button } from "@/components/ui/button";
import { MoveRight, Search, Target, TrendingUp, Zap } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainGrid = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">The Challenge</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="p-6 bg-white rounded-card border">
          <Search className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Search Invisibility</h3>
          <p className="text-foreground">Your customers can't find you when they need you most. AI-powered search is changing how people discover solutions.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <Target className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Intent Mismatch</h3>
          <p className="text-foreground">Traditional SEO misses the nuanced ways customers express their needs in the age of conversational AI.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <TrendingUp className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Competitive Disadvantage</h3>
          <p className="text-foreground">Competitors using AI discovery strategies are capturing market share while you're stuck with outdated approaches.</p>
        </div>
      </div>
    </div>
  </section>
);

const MethodSteps = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Our Method</h2>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">1</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">AI Intent Analysis</h3>
          <p className="text-foreground">We analyze how AI systems understand and categorize customer queries to position your brand optimally.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">2</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Discovery Optimization</h3>
          <p className="text-foreground">We optimize your content and presence across AI-powered search platforms and recommendation engines.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">3</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Continuous Refinement</h3>
          <p className="text-foreground">We monitor AI algorithm changes and continuously refine your discovery strategy for maximum visibility.</p>
        </div>
      </div>
    </div>
  </section>
);





const AIDiscovery = () => {
  const serviceSchema = createServiceSchema(
    "AI Discovery & Search",
    "Be the first answer—and the last click. Advanced AI-powered search optimization and discovery strategies."
  );

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="AI Discovery & Search | FunnelVision Services"
        description="Be the first answer—and the last click. Advanced AI-powered search optimization and discovery strategies that position your brand optimally in AI systems."
        jsonLd={serviceSchema}
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader 
          title="AI Discovery & Search" 
          subtitle="Be the first answer—and the last click." 
        />
        <PainGrid />
        <MethodSteps />


        <CTADemo />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default AIDiscovery;
