import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { Button } from "@/components/ui/button";
import { MoveRight, FileText, Search, TrendingUp, Users } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainGrid = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">The Challenge</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="p-6 bg-white rounded-card border">
          <Search className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Low Search Visibility</h3>
          <p className="text-muted-foreground">Your content isn't ranking for the keywords that matter most to your business and target audience.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <FileText className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Generic Content</h3>
          <p className="text-muted-foreground">Your content doesn't differentiate you from competitors or establish thought leadership in your industry.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <Users className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Poor Engagement</h3>
          <p className="text-muted-foreground">Visitors find your content but don't engage, share, or convert because it doesn't resonate with their needs.</p>
        </div>
      </div>
    </div>
  </section>
);

const MethodSteps = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Our Method</h2>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">1</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Strategic Content Planning</h3>
          <p className="text-muted-foreground">We research your audience, competitors, and opportunities to create a content strategy that drives results.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">2</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Authority-Building Content</h3>
          <p className="text-muted-foreground">We create high-quality, expert content that establishes your brand as a trusted authority in your field.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">3</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Technical SEO Optimization</h3>
          <p className="text-muted-foreground">We optimize your site's technical foundation to ensure search engines can find, crawl, and rank your content.</p>
        </div>
      </div>
    </div>
  </section>
);





const SeoContent = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader 
          title="SEO & Content Engineering" 
          subtitle="Strategic content creation and SEO optimization that drives organic growth and authority." 
        />
        <PainGrid />
        <MethodSteps />


        <CTADemo />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default SeoContent;
