# Image Insertion Guide for FunnelVision Blog

This guide explains how to insert images into blog articles with proper spacing and formatting.

## Image Syntax

### Basic Image Insertion
Use standard Markdown syntax for images:
```markdown
![Alt text](image-url)
```

### Example
```markdown
![FunnelVision Dashboard](https://example.com/dashboard.jpg)
```

## Image Spacing & Formatting

The markdown parser automatically handles image spacing:

- **Automatic Spacing**: Images get `my-8` (margin top/bottom) for proper separation
- **Responsive Design**: Images are `w-full` (full width) and responsive
- **Rounded Corners**: Images have `rounded-lg` for modern appearance
- **Shadow**: Subtle `shadow-sm` for depth

## Image Storage Options

### 1. Supabase Storage (Recommended)
Upload images to your Supabase storage bucket:

1. Go to Supabase Dashboard → Storage
2. Create a bucket called `blog-images` (if not exists)
3. Upload your image
4. Copy the public URL
5. Use in your article

**Example URL:**
```
https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/my-image.jpg
```

### 2. External CDN
Use any external image hosting service:
- Cloudinary
- AWS S3
- Google Cloud Storage
- Imgur (for testing)

### 3. Local Assets (Not Recommended for Production)
Place images in `public/` folder and reference with `/image.jpg`

## Best Practices

### Image Optimization
- **Format**: Use WebP for best compression, fallback to JPG/PNG
- **Size**: Optimize for web (typically under 500KB)
- **Dimensions**: Max width 1200px for blog content
- **Alt Text**: Always include descriptive alt text for accessibility

### File Naming
- Use descriptive names: `conversion-rate-chart.jpg`
- Avoid spaces: Use hyphens or underscores
- Include article context: `seo-guide-keyword-research.png`

## Article Content Examples

### Single Image
```markdown
# SEO Strategy Guide

Search engine optimization is crucial for business growth.

![SEO Performance Dashboard](https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/seo-dashboard.jpg)

The dashboard above shows typical SEO metrics you should track.
```

### Multiple Images
```markdown
## Before and After Results

Here's what we achieved for our client:

![Before Optimization](https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/before-optimization.jpg)

After implementing our strategies:

![After Optimization](https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/after-optimization.jpg)

The results speak for themselves.
```

### Images with Captions
```markdown
![Google Analytics Dashboard](https://vnhswtyhvrhknuvvzxse.supabase.co/storage/v1/object/public/blog-images/analytics-dashboard.jpg)

*Figure 1: Google Analytics showing 300% traffic increase after optimization*
```

## Technical Implementation

### Markdown Parser Handling
The `parseMarkdown` function in `src/lib/markdown-parser.ts` processes images:

```javascript
// Images with proper spacing
html = html.replace(/!\[(.*?)\]\((.*?)\)/g, 
  '<div class="my-8"><img src="$2" alt="$1" class="w-full rounded-lg shadow-sm" /></div>'
);
```

### CSS Classes Applied
- `my-8`: Margin top/bottom (2rem each)
- `w-full`: Full width within container
- `rounded-lg`: Large border radius
- `shadow-sm`: Subtle shadow effect

## Supabase Storage Setup

### Creating Storage Bucket
1. Go to Supabase Dashboard
2. Navigate to Storage
3. Click "Create Bucket"
4. Name: `blog-images`
5. Set as Public bucket
6. Configure RLS policies if needed

### Upload Process
1. Click on `blog-images` bucket
2. Click "Upload file"
3. Select your optimized image
4. Copy the public URL
5. Use in your article

### URL Structure
```
https://[project-id].supabase.co/storage/v1/object/public/blog-images/[filename]
```

## Image SEO Optimization

### Alt Text Best Practices
- Be descriptive but concise
- Include relevant keywords naturally
- Describe what the image shows
- Don't start with "Image of" or "Picture of"

**Good Examples:**
```markdown
![Conversion rate optimization dashboard showing 45% improvement](url)
![Google Ads campaign performance metrics for e-commerce store](url)
![Email marketing automation workflow diagram](url)
```

**Bad Examples:**
```markdown
![Image of dashboard](url)
![Screenshot](url)
![Picture showing results](url)
```

## Responsive Behavior

### Desktop
- Images display at full content width (max 1200px)
- Proper spacing above and below
- Rounded corners and subtle shadow

### Mobile
- Images scale to fit screen width
- Maintain aspect ratio
- Touch-friendly with proper spacing

## Troubleshooting

### Image Not Displaying
1. Check URL is accessible
2. Verify image format is supported (JPG, PNG, WebP, GIF)
3. Ensure proper markdown syntax
4. Check browser console for errors

### Spacing Issues
- The parser automatically adds spacing
- Don't add extra line breaks around images
- Use standard markdown syntax

### Performance Issues
- Optimize image file sizes
- Use appropriate formats (WebP preferred)
- Consider lazy loading for multiple images

## Future Enhancements

### Planned Features
- Image lazy loading
- Automatic WebP conversion
- Image compression on upload
- Gallery/carousel support
- Image captions styling

### Advanced Usage
For complex image layouts, you can use HTML directly in markdown:

```html
<div class="grid grid-cols-2 gap-4 my-8">
  <img src="image1.jpg" alt="Before" class="rounded-lg" />
  <img src="image2.jpg" alt="After" class="rounded-lg" />
</div>
```

This guide ensures consistent, professional image presentation across all blog articles while maintaining optimal performance and SEO benefits.
