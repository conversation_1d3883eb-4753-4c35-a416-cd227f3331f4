import { useState, useEffect } from 'react';
import { BlogCategory } from '@/types/blog';
import { getAllArticles, getArticlesByCategory, getFeaturedArticle } from '@/lib/blog-api';
import { BlogFeaturedCard } from '@/components/ui/blog-featured-card';
import { BlogCard } from '@/components/ui/blog-card';
import { BlogCategoryTabs } from '@/components/ui/blog-category-tabs';
import { EnhancedNavbar } from '@/components/ui/enhanced-navbar';
import { Footer2 } from '@/components/ui/shadcnblocks-com-footer2';
import { SEOHead } from '@/components/ui/seo-head';

export default function Blog() {
  const [activeCategory, setActiveCategory] = useState<'all' | BlogCategory>('all');
  const [articles, setArticles] = useState<BlogArticle[]>([]);
  const [featuredArticle, setFeaturedArticle] = useState<BlogArticle | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadArticles = async () => {
      setLoading(true);
      try {
        const [featured, allArticles] = await Promise.all([
          getFeaturedArticle(),
          activeCategory === 'all' ? getAllArticles() : getArticlesByCategory(activeCategory)
        ]);

        setFeaturedArticle(featured);
        setArticles(allArticles);
      } catch (error) {
        console.error('Error loading articles:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticles();
  }, [activeCategory]);

  // Filter out featured article from regular articles list
  const regularArticles = articles.filter(article => article.id !== featuredArticle?.id);

  // Create blog schema
  const blogSchema = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "FunnelVision Blog",
    "description": "Expert insights on search marketing, AI discovery, conversion optimization, and growth strategies for DTC eCommerce brands.",
    "url": "https://funnelvision.com/blog",
    "publisher": {
      "@type": "Organization",
      "name": "FunnelVision",
      "url": "https://funnelvision.com"
    },
    "blogPost": articles.map(article => ({
      "@type": "BlogPosting",
      "headline": article.title,
      "description": article.description,
      "url": `https://funnelvision.com/blog/${article.slug}`,
      "datePublished": article.publishedAt,
      "author": {
        "@type": "Person",
        "name": article.author.name
      }
    }))
  };

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="FunnelVision Blog | Search Marketing & Growth Insights"
        description="Expert insights on search marketing, AI discovery, conversion optimization, and growth strategies for DTC eCommerce brands."
        jsonLd={blogSchema}
      />
      <EnhancedNavbar />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
                FunnelVision Blog
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Expert insights on conversion optimization, digital marketing, and growth strategies 
                to help you scale your business.
              </p>
            </div>

            {/* Featured Article */}
            {featuredArticle && (
              <BlogFeaturedCard article={featuredArticle} />
            )}

            {/* Category Tabs */}
            <BlogCategoryTabs
              activeCategory={activeCategory}
              onCategoryChange={setActiveCategory}
            />

            {/* Articles Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="space-y-3">
                    <div className="bg-muted rounded-md aspect-video animate-pulse" />
                    <div className="h-4 bg-muted rounded animate-pulse" />
                    <div className="h-3 bg-muted rounded w-2/3 animate-pulse" />
                  </div>
                ))}
              </div>
            ) : regularArticles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {regularArticles.map((article) => (
                  <BlogCard key={article.id} article={article} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <p className="text-lg text-muted-foreground">
                  No articles found in this category yet.
                </p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer2 />
    </div>
  );
}
