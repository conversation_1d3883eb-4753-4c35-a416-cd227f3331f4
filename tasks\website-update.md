FunnelVision Website Update – Master Task List

For: Augment Agent working in the FVAdmin2025/hero-component-kit repositoryGoal: Replace placeholders with final copy, build service & case‑study pages, wire CTAs, update meta/schema, enforce style & performance.

0 · Pre‑flight Checklist

Pull latest main branch.

Work on a feature branch feature/site-copy-refresh.

Run npm run dev locally; ensure build passes before edits.

1 · Replace Placeholder Copy

Location

Action

src/pages/index.tsx – Hero

Replace headline & sub‑line with:Better Funnels. Bigger Profits.Search & AI‑marketing that turns customer intent into margin—at scale.

Same file – Problem section

Edit dynamic text or static paragraphs to real pain points (remove lorem).

Mid‑page CTA & Footer CTA Banner

Replace headings/body/buttons with approved FunnelVision copy.Ensure one primary CTA label: Book Profit‑Action Plan.

2 · Services (Bento Cards ➜ Pages)

Homepage Cards (components/ServicesGrid.tsx or similar)

Rename titles & descriptions to:

AI Discovery & Search

Paid Search Orchestration

CRO & UX Loop

Web Development

SEO & Content Engineering

Data & Analytics

Change CTA text to Learn More.

Link each card to /services/{slug} (e.g., /services/ai-discovery).

Create Pages under src/pages/services/:

ai-discovery.tsx

paid-search.tsx

cro-ux.tsx

web-development.tsx

seo-content.tsx

data-analytics.tsx

Each page structure:

<PageHeader title="AI Discovery & Search" subtitle="Be the first answer—and the last click." />
<PainGrid />
<MethodSteps />
<KPIBanner />
<CaseSnippet slug="metomic" />
<CTASection />

Re‑use global components & utility classes; no new CSS.

Nav Dropdown (components/Navbar.tsx): list the six services, linking to pages above.

3 · Case Studies Carousel ➜ Pages

Carousel (components/CaseStudiesCarousel.tsx)

Update card data → real titles, images, logos.

Set ctaLink to /case-studies/{slug}.

Create Pages under src/pages/case-studies/:

boulies.tsx, bunkie-life.tsx, etc.

Use shared layout; include H1, challenge, solution, results, images.

Leave placeholders for CMS hook but export static data array now for build.

4 · Book‑a‑Call Page

Path: src/pages/book-a-call.tsx.

H1: Schedule Your Profit‑Action Plan.

Intro paragraph (2‑3 sentences).

<div id="calendly-placeholder" className="h-[700px] bg-muted/20 rounded-md flex items-center justify-center">Calendly Embed Placeholder</div>

Below: testimonial slider (<Testimonials />) + mini FAQ.

Ensure same Header/Footer layout.

Wire all CTAs:

Navbar, mid‑page, footer, service pages, case pages → /book-a-call.

Remove secondary buttons (e.g., “Sign up here”).

5 · Meta & Schema

Add <Helmet> (or Next Head) blocks per page:

Home: title = “Search & AI‑Marketing | FunnelVision Agency”

Services: “{Service} | FunnelVision Services”

Case: “Case Study: {Client} | FunnelVision”

Replace og:image with /assets/og-default.jpg (provide if missing).

Insert Organization JSON‑LD once in _document or Layout.

For blog posts, generate Article schema using existing front‑matter.

6 · Blog SEO & Crawlability

Verify each post has single <h1>.

Ensure markdown headings descend logically.

Add dynamic meta via getStaticProps data.

Confirm content present without JS (SSR ok via Next; if CSR, consider pre‑render later).

Internal‑link service mentions.

7 · Style Consistency & Performance Pass

Scan all pages for heading font‑weights; unify (font-bold) unless designed otherwise.

Confirm single font family (Inter) via tailwind.config.

Remove outdated demo components (e.g., NavbarDemo, pricing pages).

Optimize new images → next/image or <img loading="lazy">.

Run pnpm run lint && pnpm run build; fix any TypeScript or ESLint errors.

Check Lighthouse: aim > 90 Performance, > 95 Accessibility.

8 · Final QA

Click every nav/service/case/CTA link → no 404s.

Mobile responsive check (widths 320–1440).

View‑source: verify meta & JSON‑LD present.

Push branch → open PR; request review.