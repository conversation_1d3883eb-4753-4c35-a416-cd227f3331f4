import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { But<PERSON> } from "@/components/ui/button";
import { MoveRight, Eye, Users, BarChart3, Zap } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainGrid = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">The Challenge</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="p-6 bg-white rounded-card border">
          <Eye className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">High Bounce Rates</h3>
          <p className="text-muted-foreground">Visitors land on your site but leave immediately because the experience doesn't match their expectations or needs.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <Users className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Poor User Experience</h3>
          <p className="text-muted-foreground">Confusing navigation, slow load times, and unclear messaging create friction that prevents conversions.</p>
        </div>
        <div className="p-6 bg-white rounded-card border">
          <BarChart3 className="h-8 w-8 text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-3">Conversion Bottlenecks</h3>
          <p className="text-muted-foreground">You're driving traffic but can't identify where users drop off or why they don't complete desired actions.</p>
        </div>
      </div>
    </div>
  </section>
);

const MethodSteps = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-12">Our Method</h2>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">1</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">User Behavior Analysis</h3>
          <p className="text-muted-foreground">We analyze user journeys, heatmaps, and conversion funnels to identify optimization opportunities.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">2</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Experience Optimization</h3>
          <p className="text-muted-foreground">We design and implement UX improvements that reduce friction and guide users toward conversion.</p>
        </div>
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl font-bold text-primary-foreground">3</span>
          </div>
          <h3 className="text-xl font-semibold mb-4">Continuous Testing</h3>
          <p className="text-muted-foreground">We run systematic A/B tests and multivariate experiments to continuously improve performance.</p>
        </div>
      </div>
    </div>
  </section>
);





const CroUx = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader 
          title="CRO & UX Loop" 
          subtitle="Continuous optimization cycles that improve user experience and maximize conversion rates." 
        />
        <PainGrid />
        <MethodSteps />


        <CTADemo />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default CroUx;
